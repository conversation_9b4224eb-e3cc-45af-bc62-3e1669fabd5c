"use client";

import React from "react";
import { motion } from "framer-motion";
import { Check, X } from "lucide-react";

const ComparisonSection = () => {
  const comparisonData = [
    {
      feature: "Судалгааны арга зүй",
      traditional: "Уламжлалт арга зүй",
      ginsight: "Олон улсын стандартын дагуу",
      traditionalIcon: <X className="w-5 h-5 text-red-500" />,
      ginsightIcon: <Check className="w-5 h-5 text-green-500" />
    },
    {
      feature: "Өгөгдлийн чанар",
      traditional: "Хязгаарлагдмал",
      ginsight: "Өндөр чанартай, баталгаатай",
      traditionalIcon: <X className="w-5 h-5 text-red-500" />,
      ginsightIcon: <Check className="w-5 h-5 text-green-500" />
    },
    {
      feature: "Хугацаа",
      traditional: "2-3 сар",
      ginsight: "2-4 долоо хоног",
      traditionalIcon: <X className="w-5 h-5 text-red-500" />,
      ginsightIcon: <Check className="w-5 h-5 text-green-500" />
    },
    {
      feature: "Үнэ",
      traditional: "Өндөр өртөг",
      ginsight: "Хэмнэлттэй үнэ",
      traditionalIcon: <X className="w-5 h-5 text-red-500" />,
      ginsightIcon: <Check className="w-5 h-5 text-green-500" />
    },
    {
      feature: "Тайлан",
      traditional: "Энгийн тайлан",
      ginsight: "Интерактив дүн шинжилгээ",
      traditionalIcon: <X className="w-5 h-5 text-red-500" />,
      ginsightIcon: <Check className="w-5 h-5 text-green-500" />
    },
    {
      feature: "Дэмжлэг",
      traditional: "Хязгаарлагдмал",
      ginsight: "24/7 мэргэжлийн дэмжлэг",
      traditionalIcon: <X className="w-5 h-5 text-red-500" />,
      ginsightIcon: <Check className="w-5 h-5 text-green-500" />
    }
  ];

  return (
    <section id="comparison" className="py-24 bg-gray-900">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="mb-4"
          >
            <span className="text-sm font-medium text-gray-400 bg-gray-800 px-3 py-1 rounded-full">
              Харьцуулалт
            </span>
          </motion.div>

          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            viewport={{ once: true }}
            className="text-4xl md:text-5xl font-bold text-white mb-6"
          >
            Яагаад G-Insight
            <br />
            <span className="text-gray-400">сонгох ёстой вэ?</span>
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="text-xl text-gray-300 max-w-3xl mx-auto"
          >
            Уламжлалт судалгааны аргуудтай харьцуулахад G-Insight нь илүү хурдан,
            найдвартай, хэмнэлттэй шийдэл санал болгодог.
          </motion.p>
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="max-w-5xl mx-auto"
        >
          <div className="bg-gray-800 rounded-2xl shadow-xl border border-gray-700 overflow-hidden">
            {/* Header */}
            <div className="grid grid-cols-3 bg-gray-700 border-b border-gray-600">
              <div className="p-6">
                <h3 className="text-lg font-semibold text-white">Шинж чанар</h3>
              </div>
              <div className="p-6 border-l border-gray-600">
                <h3 className="text-lg font-semibold text-gray-300">Уламжлалт арга</h3>
              </div>
              <div className="p-6 border-l border-gray-600 bg-white text-black">
                <h3 className="text-lg font-semibold">G-Insight</h3>
              </div>
            </div>

            {/* Comparison rows */}
            {comparisonData.map((item, index) => (
              <motion.div
                key={item.feature}
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="grid grid-cols-3 border-b border-gray-600 last:border-b-0 hover:bg-gray-700 transition-colors"
              >
                <div className="p-6">
                  <span className="font-medium text-white">{item.feature}</span>
                </div>
                <div className="p-6 border-l border-gray-600">
                  <div className="flex items-center space-x-3">
                    {item.traditionalIcon}
                    <span className="text-gray-300">{item.traditional}</span>
                  </div>
                </div>
                <div className="p-6 border-l border-gray-600 bg-gray-700">
                  <div className="flex items-center space-x-3">
                    {item.ginsightIcon}
                    <span className="text-white font-medium">{item.ginsight}</span>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          viewport={{ once: true }}
          className="text-center mt-12"
        >
          <div className="bg-white text-black rounded-2xl p-8 max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold mb-4">
              Танд тохирох шийдлийг олъё
            </h3>
            <p className="text-gray-600 mb-6">
              Манай мэргэжилтнүүд таны хэрэгцээнд тохирсон судалгааны төлөвлөгөө боловсруулж өгнө.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-black text-white px-6 py-3 rounded-lg font-medium hover:bg-gray-800 transition-colors">
                Үнэгүй зөвлөгөө авах
              </button>
              <button className="border border-gray-300 text-black px-6 py-3 rounded-lg font-medium hover:bg-gray-100 transition-colors">
                Жишээ үзэх
              </button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default ComparisonSection;
