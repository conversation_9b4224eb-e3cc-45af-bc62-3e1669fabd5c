"use client";

import React from "react";
import { motion } from "framer-motion";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { 
  BarChart3, 
  Users, 
  Heart, 
  Building, 
  TrendingUp, 
  Eye, 
  MapPin, 
  TestTube,
  Smartphone
} from "lucide-react";

const ServicesSection = () => {
  const services = [
    {
      icon: <BarChart3 className="w-8 h-8" />,
      title: "БРЭНДИЙН ТАНИГДСАН БАЙДЛЫН СУДАЛГАА",
      description: "Брэндийн танигдсан байдлын судалгаанаас та хэрэглэгчдийн өнцгөөс танай үйл ажиллагаа явуулж буй зах зээлийн...",
      color: "bg-blue-500",
      delay: 0.1
    },
    {
      icon: <Users className="w-8 h-8" />,
      title: "ХЭРЭГЛЭГЧИЙН ЗАН ТӨЛӨВИЙН СУДАЛГАА",
      description: "Хэрэглэгчдийн зан төлөв, хэрэгцээ шаардлагыг судлан бизнесийн стратеги боловсруулахад туслах судалгаа...",
      color: "bg-green-500",
      delay: 0.2
    },
    {
      icon: <Heart className="w-8 h-8" />,
      title: "ХЭРЭГЛЭГЧИЙН СЭТГЭЛ ХАНАМЖИЙН СУДАЛГАА",
      description: "Үйлчлүүлэгчдийн сэтгэл ханамжийн түвшинг хэмжиж, үйлчилгээний чанарыг сайжруулах арга замыг олох...",
      color: "bg-red-500",
      delay: 0.3
    },
    {
      icon: <Building className="w-8 h-8" />,
      title: "АЖИЛЧДЫН СЭТГЭЛ ХАНАМЖИЙН СУДАЛГАА",
      description: "Ажилчдын сэтгэл ханамж, ажлын орчны үнэлгээ хийж, ажилчдын үр бүтээмжийг нэмэгдүүлэх арга замыг олох...",
      color: "bg-purple-500",
      delay: 0.4
    },
    {
      icon: <TrendingUp className="w-8 h-8" />,
      title: "ЗАХ ЗЭЭЛИЙН БАГТААМЖИЙН СУДАЛГАА",
      description: "Зах зээлийн хэмжээ, өсөлтийн боломж, өрсөлдөөний нөхцөл байдлыг судлан бизнесийн боломжийг тодорхойлох...",
      color: "bg-orange-500",
      delay: 0.5
    },
    {
      icon: <Eye className="w-8 h-8" />,
      title: "НУУЦ ҮЙЛЧЛҮҮЛЭГЧИЙН СУДАЛГАА",
      description: "Үйлчилгээний чанар, ажилчдын хандлагыг нууцаар үнэлж, бодит байдлыг тодорхойлох судалгаа...",
      color: "bg-indigo-500",
      delay: 0.6
    },
    {
      icon: <MapPin className="w-8 h-8" />,
      title: "ҮЙЛЧИЛГЭЭНИЙ ЦЭГИЙГ ТОДОРХОЙЛОХ СУДАЛГАА",
      description: "Хэрэглэгчдийн хэрэгцээ, байршлын давуу талыг судлан оновчтой үйлчилгээний цэгийг тодорхойлох...",
      color: "bg-teal-500",
      delay: 0.7
    },
    {
      icon: <TestTube className="w-8 h-8" />,
      title: "ШИНЭ БҮТЭЭГДЭХҮҮНИЙ ТЕСТ СУДАЛГАА",
      description: "Шинэ бүтээгдэхүүний зах зээлд гарахын өмнө хэрэглэгчдийн хүлээн авалтыг судлах тест судалгаа...",
      color: "bg-pink-500",
      delay: 0.8
    },
    {
      icon: <Smartphone className="w-8 h-8" />,
      title: "ХЭРЭГЛЭЭНИЙ ТЕСТ СУДАЛГАА",
      description: "Бүтээгдэхүүний хэрэглээ, ашиглалтын туршлагыг судлан сайжруулах талуудыг тодорхойлох судалгаа...",
      color: "bg-cyan-500",
      delay: 0.9
    }
  ];

  return (
    <section className="py-20 bg-gradient-to-b from-white to-gray-50">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <Badge variant="outline" className="mb-4">
            Бидний үйлчилгээ
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            СУДАЛГААНЫ ТӨРЛҮҮД
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Бид олон төрлийн зах зээлийн судалгаа хийж, танай бизнесийн шийдвэр гаргахад шаардлагатай мэдээллийг өгдөг
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <motion.div
              key={service.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: service.delay }}
              viewport={{ once: true }}
              whileHover={{ y: -8, scale: 1.02 }}
              className="h-full group"
            >
              <Card className="h-full glass border border-white/20 hover:shadow-glow transition-all duration-500 hover-lift overflow-hidden relative">
                <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                <CardHeader className="relative z-10">
                  <div className={`w-16 h-16 rounded-xl ${service.color} flex items-center justify-center text-white mb-4 shadow-glow group-hover:shadow-glow-lg transition-all duration-300 group-hover:scale-110`}>
                    {service.icon}
                  </div>
                  <CardTitle className="text-lg font-bold leading-tight bg-gradient-to-r from-foreground to-muted-foreground bg-clip-text text-transparent">
                    {service.title}
                  </CardTitle>
                </CardHeader>
                <CardContent className="relative z-10">
                  <CardDescription className="text-base leading-relaxed text-muted-foreground group-hover:text-foreground transition-colors duration-300">
                    {service.description}
                  </CardDescription>
                  <Badge variant="outline" className="mt-4 glass border-white/30 hover:bg-white/10 transition-all duration-300">
                    Дэлгэрэнгүй
                  </Badge>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 1 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <p className="text-lg text-muted-foreground mb-8">
            Танай бизнесийн хэрэгцээнд тохирсон судалгааны төрлийг сонгож, 
            мэргэжлийн зөвлөгөө авахыг хүсвэл бидэнтэй холбогдоорой
          </p>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold text-lg transition-colors"
          >
            Үнийн санал авах
          </motion.button>
        </motion.div>
      </div>
    </section>
  );
};

export default ServicesSection;
