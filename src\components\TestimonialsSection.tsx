"use client";

import React from "react";
import { motion } from "framer-motion";
import { MarqueeDemo } from "@/components/ui/marquee-demo";

const TestimonialsSection = () => {

  return (
    <section id="reviews" className="py-24 bg-black">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="mb-4"
          >
            <span className="text-sm font-medium text-gray-400 bg-gray-800 px-3 py-1 rounded-full">
              Trusted Partners
            </span>
          </motion.div>

          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            viewport={{ once: true }}
            className="text-4xl md:text-5xl font-bold text-white mb-6"
          >
            Powered by
            <br />
            <span className="text-gray-400">Industry Leaders</span>
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="text-xl text-gray-300 max-w-3xl mx-auto"
          >
            Built with the most trusted and cutting-edge technologies in the industry.
          </motion.p>
        </div>

        {/* Marquee Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="mb-16"
        >
          <MarqueeDemo />
        </motion.div>

        {/* Enhanced Stats Section */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          viewport={{ once: true }}
          className="relative bg-black border border-gray-800 rounded-3xl p-12 overflow-hidden"
        >
          {/* Background gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-purple-500/5 to-cyan-500/5 opacity-50" />

          {/* Animated border glow */}
          <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-cyan-500/20 opacity-0 animate-pulse" />

          <div className="relative z-10 grid grid-cols-2 md:grid-cols-4 gap-8">
            {[
              { value: "99.9%", label: "Uptime Guarantee", delay: 0.1 },
              { value: "500+", label: "Projects Delivered", delay: 0.2 },
              { value: "10+", label: "Years Experience", delay: 0.3 },
              { value: "24/7", label: "Support Available", delay: 0.4 }
            ].map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, y: 30, scale: 0.8 }}
                whileInView={{ opacity: 1, y: 0, scale: 1 }}
                transition={{
                  duration: 0.6,
                  delay: stat.delay,
                  ease: "easeOut"
                }}
                whileHover={{
                  scale: 1.05,
                  transition: { duration: 0.2 }
                }}
                viewport={{ once: true }}
                className="text-center group cursor-pointer"
              >
                {/* Stat container with hover effects */}
                <div className="relative p-6 rounded-2xl bg-gray-900/50 border border-gray-700/50 backdrop-blur-sm transition-all duration-300 group-hover:border-blue-500/50 group-hover:bg-gray-800/50 group-hover:shadow-lg group-hover:shadow-blue-500/10">
                  {/* Animated background on hover */}
                  <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-blue-500/10 to-purple-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                  {/* Number with gradient text */}
                  <motion.div
                    className="relative text-4xl md:text-5xl font-bold bg-gradient-to-r from-white via-blue-100 to-cyan-100 bg-clip-text text-transparent mb-3"
                    initial={{ scale: 0.5 }}
                    whileInView={{ scale: 1 }}
                    transition={{
                      duration: 0.5,
                      delay: stat.delay + 0.2,
                      type: "spring",
                      stiffness: 200
                    }}
                    viewport={{ once: true }}
                  >
                    {stat.value}
                  </motion.div>

                  {/* Label with subtle animation */}
                  <motion.div
                    className="relative text-sm font-medium text-gray-300 group-hover:text-gray-200 transition-colors duration-300"
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    transition={{
                      duration: 0.4,
                      delay: stat.delay + 0.4
                    }}
                    viewport={{ once: true }}
                  >
                    {stat.label}
                  </motion.div>

                  {/* Subtle glow effect on hover */}
                  <div className="absolute inset-0 rounded-2xl bg-blue-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
                </div>
              </motion.div>
            ))}
          </div>

          {/* Floating particles effect */}
          <div className="absolute inset-0 overflow-hidden pointer-events-none">
            {[
              { id: 'p1', x: 20, delay: 0 },
              { id: 'p2', x: 40, delay: 0.5 },
              { id: 'p3', x: 60, delay: 1 },
              { id: 'p4', x: 80, delay: 1.5 },
              { id: 'p5', x: 30, delay: 2 },
              { id: 'p6', x: 70, delay: 2.5 }
            ].map((particle) => (
              <motion.div
                key={particle.id}
                className="absolute w-1 h-1 bg-blue-400/30 rounded-full"
                initial={{
                  x: particle.x + "%",
                  y: "100%",
                  opacity: 0
                }}
                animate={{
                  y: "-10%",
                  opacity: [0, 1, 0],
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  delay: particle.delay,
                  ease: "easeOut"
                }}
              />
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default TestimonialsSection;
