"use client";

import React from "react";
import { motion } from "framer-motion";
import RuixenPricing from "@/components/ui/ruixen-pricing-04";
import { TextHoverEffect } from "@/components/ui/text-hover-effect";

const PricingSection = () => {

  return (
    <section id="pricing" className="py-24 bg-black">
      {/* Pricing Component with wider container */}
      <div className="container mx-auto px-6 max-w-7xl">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <RuixenPricing />
        </motion.div>
      </div>

      {/* Text Hover Effect at the bottom with full width */}
      <div className="w-full px-4 mt-16">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
          className="max-w-full mx-auto"
        >
          <div className="h-[40rem] flex items-center justify-center bg-black">
            <TextHoverEffect text="ACET" />
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default PricingSection;
