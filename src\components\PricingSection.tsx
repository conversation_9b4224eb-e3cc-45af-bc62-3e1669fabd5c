"use client";

import React from "react";
import { motion } from "framer-motion";
import RuixenPricing from "@/components/ui/ruixen-pricing-04";

const PricingSection = () => {

  return (
    <section id="pricing" className="py-24 bg-black">
      <div className="container mx-auto px-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <RuixenPricing />
        </motion.div>
      </div>
    </section>
  );
};

export default PricingSection;
