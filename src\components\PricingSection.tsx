"use client";

import React from "react";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Check } from "lucide-react";

const PricingSection = () => {
  const pricingPlans = [
    {
      name: "Суурь",
      description: "Жижиг бизнест тохиромжтой",
      price: "500,000₮",
      period: "төслөөс",
      features: [
        "Энгийн судалгаа (50 хариулагч)",
        "Үндсэн тайлан",
        "1 удаагийн зөвлөгөө",
        "Имэйл дэмжлэг",
        "2 долоо хоногийн хугацаа"
      ],
      popular: false,
      buttonText: "Эхлэх",
      buttonVariant: "outline" as const
    },
    {
      name: "Стандарт",
      description: "Дунд зэргийн компанид тохиромжтой",
      price: "1,200,000₮",
      period: "төслөөс",
      features: [
        "Дэлгэрэнгүй судалгаа (200 хариулагч)",
        "Интерактив тайлан",
        "3 удаагийн зөвлөгөө",
        "Утас, имэйл дэмжлэг",
        "1 долоо хоногийн хугацаа",
        "Презентаци бэлтгэх"
      ],
      popular: true,
      buttonText: "Сонгох",
      buttonVariant: "default" as const
    },
    {
      name: "Премиум",
      description: "Том корпорацид зориулсан",
      price: "Тохиролцоно",
      period: "төслөөс",
      features: [
        "Иж бүрэн судалгаа (500+ хариулагч)",
        "Дэвшилтэт аналитик",
        "Хязгааргүй зөвлөгөө",
        "24/7 дэмжлэг",
        "Хурдан хугацаа",
        "Презентаци + семинар",
        "Дараа дагалдах үйлчилгээ"
      ],
      popular: false,
      buttonText: "Холбогдох",
      buttonVariant: "outline" as const
    }
  ];

  return (
    <section id="pricing" className="py-24 bg-gray-900">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="mb-4"
          >
            <span className="text-sm font-medium text-gray-400 bg-gray-800 px-3 py-1 rounded-full">
              Үнийн санал
            </span>
          </motion.div>

          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            viewport={{ once: true }}
            className="text-4xl md:text-5xl font-bold text-white mb-6"
          >
            Танд тохирох
            <br />
            <span className="text-gray-400">багцыг сонгоорой</span>
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="text-xl text-gray-300 max-w-3xl mx-auto"
          >
            Бизнесийн хэмжээ, хэрэгцээнд тохирсон олон төрлийн үйлчилгээний багц.
            Бүх багцад чанартай үр дүн баталгаатай.
          </motion.p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {pricingPlans.map((plan, index) => (
            <motion.div
              key={plan.name}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className={`relative bg-gray-800 rounded-2xl border-2 p-8 ${
                plan.popular
                  ? "border-white shadow-xl scale-105"
                  : "border-gray-600 hover:border-gray-500"
              } transition-all duration-300`}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <span className="bg-white text-black px-4 py-2 rounded-full text-sm font-medium">
                    Хамгийн алдартай
                  </span>
                </div>
              )}

              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-white mb-2">{plan.name}</h3>
                <p className="text-gray-300 mb-4">{plan.description}</p>
                <div className="mb-2">
                  <span className="text-4xl font-bold text-white">{plan.price}</span>
                  {plan.price !== "Тохиролцоно" && (
                    <span className="text-gray-400 ml-2">{plan.period}</span>
                  )}
                </div>
              </div>

              <ul className="space-y-4 mb-8">
                {plan.features.map((feature) => (
                  <li key={feature} className="flex items-start">
                    <Check className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-300">{feature}</span>
                  </li>
                ))}
              </ul>

              <Button
                variant={plan.buttonVariant}
                className={`w-full py-3 rounded-lg font-medium ${
                  plan.popular
                    ? "bg-white text-black hover:bg-gray-200"
                    : "border-gray-500 text-gray-300 hover:bg-gray-700"
                }`}
              >
                {plan.buttonText}
              </Button>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <div className="bg-gray-800 rounded-2xl p-8 max-w-4xl mx-auto border border-gray-700">
            <h3 className="text-2xl font-bold text-white mb-4">
              Тусгай хэрэгцээ байна уу?
            </h3>
            <p className="text-gray-300 mb-6">
              Манай мэргэжилтнүүд таны бизнесийн онцлог хэрэгцээнд тохирсон
              тусгай судалгааны төлөвлөгөө боловсруулж өгнө.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button className="bg-white text-black hover:bg-gray-200 px-8">
                Үнэгүй зөвлөгөө авах
              </Button>
              <Button variant="outline" className="border-gray-500 text-gray-300 hover:bg-gray-700 px-8">
                Жишээ харах
              </Button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default PricingSection;
